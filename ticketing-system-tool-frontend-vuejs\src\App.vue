<script lang="ts" setup>
import { useRoute } from "vue-router";
import { computed } from "vue";
import Default from "../src/layouts/default.vue";
import { getMessaging, getToken, onMessage } from "firebase/messaging";
import { vapidKey, app } from "./plugins/firebase";

const route = useRoute();
const requiresAuth = computed(() => route?.meta?.requiresAuth ?? false);
import { onMounted, onUnmounted } from "vue";

import { connectLogoutSocket, disconnectLogoutSocket } from "./utils/socket";
import { save_fcm_token } from "./api/apiClient";

const messaging = getMessaging(app);



onMounted(() => {
  Notification.requestPermission().then((permission) => {
    if (permission === "granted") {
      getToken(messaging, { vapidKey: vapidKey }).then(async (currentToken) => {
        if (currentToken) {
          console.log("FCM token:", currentToken);

          await save_fcm_token({ fcm_token: currentToken });
          // send to backend
        }
      });
    }
  });
onMessage(messaging, (payload) => {
  console.log("📨 FCM payload:", payload);

  const { title, body } = payload.notification || {};

  if (!title || !body) {
    console.warn("Missing title or body in payload");
    return;
  }

  // Check permission
  if (Notification.permission !== "granted") {
    console.warn("Notification permission not granted");
    return;
  }

  // Try showing a notification
  try {
    const notification = new Notification(title, {
      body,
      icon: "/firebase-logo.png", // Replace if you want
    });

    // Optional: handle click
    notification.onclick = () => {
      window.focus();
    };

    // Optional: sound
    // const audio = new Audio("/notification.mp3"); // must exist in public/
    // audio.play().catch((e) => console.warn("Audio play failed:", e));
  } catch (err) {
    console.error("Error showing notification:", err);
  }
});


  const user = JSON.parse(localStorage.getItem("user") || "{}");
  if (user?.id) {
    console.log("Connecting logout socket for user:", user.id);
    connectLogoutSocket(user.id);
  }
});

// Disconnect socket when component unmounts
onUnmounted(() => {
  console.log("Disconnecting logout socket");
  disconnectLogoutSocket();
});

// Watch for user changes in localStorage
window.addEventListener("storage", (event) => {
  if (event.key === "user") {
    const user = JSON.parse(event.newValue || "{}");
    if (user?.id) {
      console.log("User changed, reconnecting logout socket");
      connectLogoutSocket(user.id);
    } else {
      disconnectLogoutSocket();
    }
  }
});
</script>

<template>
  <div class="app-main">
    <!-- Render Default layout for authenticated routes -->
    <div v-if="requiresAuth">
      <Default>
        <router-view />
      </Default>
    </div>
    <!-- Directly render public routes -->
    <router-view v-else />
  </div>
</template>

<style>
@import "./assets/css/font.css";
@import "./styles/style.scss";
.app-page-layout {
  position: absolute;
  margin: 0 10px;
  width: 100%;
  border-radius: 5px;
}
</style>
