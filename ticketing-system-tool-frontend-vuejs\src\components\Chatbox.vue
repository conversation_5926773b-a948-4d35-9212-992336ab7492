<template>
  <div class="chat-container">
    <div class="chat-box">
      <div class="chat-single-container">
        <ul>
          <li
            v-for="(msg, index) in messages"
            :key="index"
            :class="{
              'received-message': msg.sender_id === userId,
              'sent-message': msg.sender_id !== userId,
            }"
          >
            <div class="user-comment-picture">
              <span
                class="user-dp"
                :class="msg.sender_id === userId ? 'sender-dp' : 'receiver-dp'"
              >
                {{ msg.sender_name?.charAt(0).toUpperCase() || "U" }}
              </span>
            </div>
            <div class="user-comment-wrapper">
              <div class="row justify-space-between user-comment-info">
                <div class="col-sm-12 col-md-auto">
                  <h6>{{ msg.sender_name || "Unknown" }}</h6>
                </div>
                <div class="col-sm-12 col-md-auto">
                  <ul class="user-comment-misc-info">
                    <li>
                      <i class="fa-regular fa-calendar" />
                      {{ formatTimestamp(msg.timestamp) }}
                    </li>
                  </ul>
                </div>
              </div>
              <div class="row user-comment-description">
                <div class="col-sm-12 col-md-auto">
                  <!-- Replace the simple paragraph with a div that preserves whitespace -->
                  <div class="message-content" v-html="msg.message"></div>

                  <div v-if="msg.attachement && msg.attachement.length">
                    <div
                      v-for="(file, i) in msg.attachement"
                      :key="i"
                      class="attachement"
                    >
                      <a
                        :href="file.file_data"
                        :download="file.file_name"
                        target="_blank"
                        class="file-url"
                      >
                        📎 {{ file.original_filename }}
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </div>

      <!-- Chat Form -->
      <div class="chat-form-wrapper">
        <div class="form-group">
          <label class="form-label">{{ strings.editTicket.commentText }}</label>
          <TextEditor ref="textEditorRef" v-model="newMessage" />
        </div>
        <div class="form-group mt-3">
          <!-- <label class="form-label">Documents <small>(Optional)</small></label>
          <input type="file" @change="handleFileUpload" /> -->
          <div>
            <!-- Document Upload -->
            <!-- Hidden file input -->
            <input
              ref="fileInput"
              type="file"
              multiple
              accept="*"
              style="display: none"
              @change="handleFileSelect"
            />

            <!-- Upload Button -->
            <div class="file-upload-container mb-0">
              <v-btn @click="triggerFilePicker" variant="outlined">
                {{ strings.common.uploadDocumentButton }}
              </v-btn>
              <!-- <span class="file-size-limit">(Maximum file size: 2MB)</span> -->
            </div>
            <div class="helper-text">
              <label>{{ strings.common.filesizeText }}</label>
            </div>

            <!-- Show selected files -->
            <div v-if="state.attachement.length" class="file-preview-wrapper">
              <div
                v-for="(file, index) in state.attachement"
                :key="index"
                class="file-preview"
              >
                <!-- Image Preview -->
                <img
                  v-if="file && file.type.startsWith('image/')"
                  :src="getImageUrl(file)"
                  alt="Preview"
                  class="preview-image"
                />

                <!-- Icon for Non-Images -->
                <div v-else class="file-icon">
                  <v-icon size="40">
                    {{
                      file.type.includes("pdf")
                        ? "mdi-file-pdf"
                        : file.type.includes("word")
                        ? "mdi-file-word"
                        : file.type.includes("spreadsheetml")
                        ? "mdi-file-excel"
                        : "mdi-file-document"
                    }}
                  </v-icon>
                </div>

                <!-- File Name with Tooltip -->
                <v-tooltip location="bottom">
                  <template #activator="{ props }">
                    <div class="file-name" v-bind="props">
                      {{ file.name }}
                    </div>
                  </template>
                  {{ file.name }}
                </v-tooltip>

                <!-- Remove Button -->
                <v-btn
                  icon
                  size="x-small"
                  class="remove-btn"
                  @click="removeFile(index)"
                >
                  <v-icon size="14">mdi-close</v-icon>
                </v-btn>
              </div>
            </div>

            <!-- Validation Error
  <label v-if="v$.attachement.$errors.length" class="error-text">
    {{ v$.attachement.$errors[0].$message }}
  </label> -->
          </div>
        </div>
        <!-- Send Message Button -->
        <div class="btn-toolbar mt-3 justify-content-center">
          <v-btn variant="tonal" @click="sendChatMessage">
            {{ strings.common.sendMessageButton }}
          </v-btn>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { ref, onMounted, onUnmounted, computed, reactive } from "vue";
import {
  socketConnected,
  connectSocket,
  sendMessage,
  disconnectSocket,
} from "../utils/socket";
import apiClient, {
  apiClientFormData,
  getTicketByChat,
} from "../api/apiClient";
import { useRoute } from "vue-router";
import { helpers } from "@vuelidate/validators";
import { API_BASE_URL } from "../api/apiconfig";
import { useToast } from "vue-toastification";
import TextEditor from "@/components/TextEditor.vue";
import strings from "../assets/strings.json";

interface RouteParams {
  ticket_id: string;
}

export default {
  name: "Chatbox",
  components: {
    TextEditor,
  },
  setup() {
    const route = useRoute();
    const params = route.params as unknown as RouteParams;
    const ticketId = Number(params.ticket_id);
    const fileErrorMessage = ref<string | null>(null);
    const maxFileSize = 2 * 1024 * 1024;
    const fileModel = ref<File[] | null>(null);
    const toast = useToast();
    const textEditorRef = ref();

    const state = reactive({
      attachement: [] as File[],
    });

    // const allowedTypes = [
    //   "application/pdf",
    //   "image/png",
    //   "image/jpeg",
    //   "image/jpg",
    //   "application/msword",
    //   "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    //   "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    //   "text/plain",
    // ];
    const allowedTypes: string[] = []; // No restriction

    // const v$ = useVuelidate(state);

    const handleFileSelect = (event: Event) => {
      const input = event.target as HTMLInputElement;
      const files = input.files;
      if (!files) return;

      const selectedFiles = Array.from(files); // ✅ Real File objects

      const invalidFiles: File[] = [];
      const oversizedFiles: File[] = [];

      const newValidFiles = selectedFiles.filter((file) => {
        // Check file size separately
        if (file.size > maxFileSize) {
          oversizedFiles.push(file);
          return false;
        }

        // Check other validation criteria
        // Check other validation criteria
        const isValid =
          allowedTypes.length === 0 || allowedTypes.includes(file.type);
        if (!isValid) {
          invalidFiles.push(file);
        }
        return isValid;
      });

      // Prevent duplicates (by name + size)
      const existingFileKeys = new Set(
        state.attachement.map((f: any) => `${f.name}-${f.size}`)
      );

      const uniqueFiles = newValidFiles.filter(
        (file) => !existingFileKeys.has(`${file.name}-${file.size}`)
      );

      // Append to attachement
      state.attachement = [...state.attachement, ...uniqueFiles];

      // Show specific error for oversized files
      if (oversizedFiles.length > 0) {
        const names = oversizedFiles.map((f) => f.name).join(", ");
        toast.error(`The following files exceed the 2MB size limit: ${names}`);
      }

      // Show warning if any files were invalid for other reasons
      if (invalidFiles.length > 0) {
        const names = invalidFiles.map((f) => f.name).join(", ");
        toast.warning(`Some files have invalid formats: ${names}`);
      }

      // Reset input so same file can be selected again
      input.value = "";
    };
    const getImageUrl = (file: File) => {
      try {
        return URL.createObjectURL(file);
      } catch (error) {
        console.error("Invalid file for preview:", error);
        return "";
      }
    };

    const removeFile = (index: number) => {
      state.attachement.splice(index, 1);
    };
    const triggerFilePicker = () => {
      fileInput.value?.click();
    };

    const rules = {
      // attachement: {
      //   fileSize: (value: File[] | null) => {
      //     if (!value || !Array.isArray(value)) return true;
      //     return value.every((file) => file.size <= maxFileSize)
      //       ? true
      //       : "File must be 2MB or less";
      //   },
      // },
      attachement: {
        fileSize: helpers.withMessage(
          "Each file must be 2MB or less",
          (value: File[] | null) =>
            !value ||
            (Array.isArray(value) &&
              value.every((file) => file.size <= maxFileSize))
        ),
      },
    };

    const messages = ref<
      {
        sender_id: number;
        sender_name: string;
        message: string;
        file_data?: string;
        file_name?: string;
        attachement?: any[]; // Added attachements property
        timestamp: string;
      }[]
    >([]);

    const user = ref<any>({});
    onMounted(() => {
      const storedUser = JSON.parse(localStorage.getItem("user") || "{}");
      user.value = storedUser;
    });

    const userId = computed(() => user.value?.id ?? null);
    const newMessage = ref("");
    const selectedFile = ref<File | null>(null);

    const fetchChatHistory = async () => {
      try {
        const response = await apiClientFormData.get(
          `ticket/${ticketId}/messages/`
        );

        const newMessages = response.data.map((msg: any) => ({
          sender_id: msg.sender_id,
          sender_name: msg.sender_name || "Unknown",
          message: msg.message || "",
          attachement: Array.isArray(msg.attachement)
            ? msg.attachement.map((att: any) => ({
                file_data: att.file_data.startsWith("http")
                  ? att.file_data
                  : `${API_BASE_URL.replace(/\/api\/$/, "")}${att.file_data}`,
                file_name: att.file_name,
                original_filename: att.original_filename,
              }))
            : [],
          timestamp: msg.timestamp || new Date().toISOString(),
        }));

        messages.value = [...newMessages];
        console.log("Chat history fetched:", messages.value);
      } catch (error: any) {
        console.error("Error fetching chat history:", error);
      }
    };

    const fetchSenderName = async (senderId: number) => {
      try {
        const response = await apiClient.get(`users/${senderId}`);
        return response.data.data.first_name;
      } catch (error: any) {
        console.error("Error fetching sender name:", error);
        return "Unknown";
      }
    };

    const formatTimestamp = (timestamp: string) => {
      return new Intl.DateTimeFormat("en-US", {
        year: "numeric",
        month: "short",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      }).format(new Date(timestamp));
    };

    onMounted(async () => {
      await fetchChatHistory();
      connectSocket(ticketId, async (message) => {
        const senderName = await fetchSenderName(message.sender_id);

        const attachments = Array.isArray(message.attachement)
          ? message.attachement.map((att: any) => ({
              file_data: att.file_data
                ? att.file_data.startsWith("http")
                  ? att.file_data
                  : `${API_BASE_URL.replace(/\/api\/$/, "")}${att.file_data}`
                : "",
              file_name: att.file_name || "Unnamed file",
            }))
          : [];

        messages.value.push({
          sender_id: message.sender_id,
          sender_name: senderName,
          message: message.message,
          attachement: attachments,
          timestamp: message.timestamp || new Date().toISOString(),
        });
      });
    });

    onUnmounted(() => {
      disconnectSocket();
    });

    const handleFileUpload = (event: Event) => {
      const fileInput = event.target as HTMLInputElement;
      fileErrorMessage.value = null; // reset error

      if (fileInput.files && fileInput.files.length > 0) {
        const file = fileInput.files[0];
        if (file.size > maxFileSize) {
          fileErrorMessage.value = "Each file must be 2MB or less";
          selectedFile.value = null;
          fileModel.value = null;
          return;
        }
        selectedFile.value = file;
      }
    };

    const fileInput = ref<HTMLInputElement | null>(null);

    const toBase64 = (
      file: File
    ): Promise<{ file_data: string; file_name: string }> => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          const result = reader.result as string;
          resolve({ file_data: result.split(",")[1], file_name: file.name });
        };
        reader.onerror = (error) => reject(error);
        reader.readAsDataURL(file);
      });
    };

    const sendChatMessage = async () => {
      const messageContent = textEditorRef.value?.getContent() || "";
      if (
        (!messageContent || messageContent === "<p><br></p>") &&
        state.attachement.length === 0
      )
        return;

      const base64attachements = await Promise.all(
        state.attachement.map((file) => toBase64(file))
      );

      const sendViaSocket = (data: any) => {
        if (socketConnected.value) {
          sendMessage(data); // Send via WebSocket

          return true;
        }
        return false;
      };

      const sendViaApi = async (data: any) => {
        try {
          const response = await getTicketByChat(ticketId, data);

          const msg = response.data;
          const attachments = Array.isArray(msg.attachement)
            ? msg.attachement.map((att: any) => ({
                file_data: att.file_data.startsWith("http")
                  ? att.file_data
                  : `${API_BASE_URL.replace(/\/api\/$/, "")}${att.file_data}`,
                file_name: att.file_name,
              }))
            : [];

          // Push formatted message into chat
          messages.value.push({
            sender_id: msg.sender_id,
            sender_name: user.value?.first_name || "Unknown",
            message: Array.isArray(msg.message) ? msg.message[0] : msg.message,
            file_data: msg.file_data, // Optional single file fallback
            file_name: msg.file_name,
            attachement: attachments,
            // For multiple files
            timestamp: msg.timestamp || new Date().toISOString(),
          });
        } catch (err) {
          console.error("API message post failed:", err);
        }
      };

      const handleFiles = async () => {
        const fileDataList = await Promise.all(
          state.attachement.map((file) => {
            return new Promise<{ file_data: string; file_name: string }>(
              (resolve) => {
                const reader = new FileReader();
                reader.onload = () => {
                  resolve({
                    file_data: reader.result as string,
                    file_name: file.name,
                  });
                };
                reader.readAsDataURL(file);
              }
            );
          })
        );

        const messagePayload = {
          sender_id: userId.value,
          message: messageContent,
          attachement: fileDataList,
        };

        const sentViaSocket = sendViaSocket(messagePayload);
        if (!sentViaSocket) {
          await sendViaApi(messagePayload);
        }
      };

      if (state.attachement.length > 0) {
        await handleFiles();
      } else {
        const messagePayload = {
          sender_id: userId.value,
          message: messageContent,
          attachement: [],
        };
        const sentViaSocket = sendViaSocket(messagePayload);
        if (!sentViaSocket) {
          await sendViaApi(messagePayload);
        }
      }

      setTimeout(async () => {
        await fetchChatHistory();
      }, 300);

      // Clear input fields
      newMessage.value = "";
      // Clear editor content visually too
      const editor = document.querySelector(".ql-editor");
      if (editor) editor.innerHTML = "";

      selectedFile.value = null;
      state.attachement = [];
      if (fileInput.value) fileInput.value.value = "";
    };

    const handleEnter = (event: KeyboardEvent) => {
      if (event.shiftKey) {
        // When Shift is pressed, allow the newline to be inserted
        return;
      } else {
        event.preventDefault(); // Prevent the default newline insertion
        sendChatMessage();
      }
    };

    return {
      sendChatMessage,
      handleFileUpload,
      fileInput,
      messages,
      newMessage,
      formatTimestamp,
      userId,
      rules,
      fileErrorMessage,
      fileModel,
      handleEnter,
      state,
      handleFileSelect,
      getImageUrl,
      removeFile,
      triggerFilePicker,
      textEditorRef,
      strings,
    };
  },
};
</script>

<style scoped>
/* Add this to your existing styles */
.message-content {
  white-space: pre-line;
  word-wrap: break-word;
  word-break: break-word;
  text-align: left;
}

/* Your existing styles */
.chat-message {
  max-width: 90%;
  padding: 8px 12px;
  margin: 5px;
  border-radius: 10px;
  word-wrap: break-word;
  position: relative;
}
.message-time {
  font-size: 0.8rem;
  color: #c3c1c1;
  text-align: left;
  margin-top: 2px;
}

/* Message Timestamp */
.user-comment-misc-info li {
  font-size: 0.8rem;
  color: #231e1e;
  display: flex;
  align-items: center;
  gap: 4px;
}
.file-url {
  font-size: 0.8rem;
  color: #3498db;
  text-decoration: none;
}

.file-size-limit {
  font-size: 0.8rem;
  color: #868686;
  margin-left: 8px;
  font-style: italic;
}

.file-upload-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

/* Limit width and allow dynamic sizing */
.received-message,
.sent-message {
  display: inline-block;
  max-width: 70%; /* Max width */
  padding: 10px 14px;
  margin: 8px 0;
  border-radius: 16px;
  word-wrap: break-word;
  word-break: break-word;
  white-space: pre-line;
}

/* Align messages properly */
.received-message {
  background-color: #f1f1f1;
  align-self: flex-end;
  border-top-left-radius: 0;
  margin-left: 10px;
  text-align: left;
}

.sent-message {
  background-color: #e0f7fa;
  align-self: flex-start;
  border-top-right-radius: 0;
  margin-left: auto;
  margin-right: 10px;
  text-align: left;
}

.chat-single-container ul {
  display: flex;
  flex-direction: column;
  padding: 0;
  margin: 0;
  list-style: none;
}

.message-content ul {
  list-style-type: disc !important;
  padding-left: 20px;
  margin: 0;
}

.message-content ol {
  list-style-type: decimal !important;
  padding-left: 20px;
  margin: 0;
}

/* .received-message .user-comment-description p {
  text-align: left !important;
  color: inherit !important;
} */
</style>
