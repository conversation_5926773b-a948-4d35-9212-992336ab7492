import { initializeApp } from "firebase/app";
import { getMessaging, getToken, onMessage } from "firebase/messaging";

const firebaseConfig = {
    apiKey: "AIzaSyCMWc3XxRqiv1VVv71TrWg1ByjcfTAwP2c",
    authDomain: "nex-ticketting.firebaseapp.com",
    projectId: "nex-ticketting",
    storageBucket: "nex-ticketting.firebasestorage.app",

    messagingSenderId: "584583859864",
    appId: "1:584583859864:web:078f14e255049f66314aed",
    measurementId: "G-CVR3SWLH7X"
  };
// Initialize Firebase
export const app = initializeApp(firebaseConfig);
const messaging = getMessaging(app);
export const vapidKey="BM-jPYgSGgdTqhErmnyVYYTQTS41PsPM_HuwsolB33WRCoLvTCEsih021EfuQDNSqdblDO9njZfSD45_8NGwlWc"
// Initialize Firebase Cloud Messaging
console.log("messaging:",messaging);

 async function retrieveFcmToken(vapidKey) {
    try {
        const currentToken = await getToken(messaging, { vapidKey });
        if (currentToken) {
            console.log("FCM Token retrieved:", currentToken);
            return currentToken;
        } else {
            console.error("No FCM Token available. Request user permission.");
            return null;
        }
    }catch (error) {
        console.error("Error retrieving FCM Token:", error);
        return null;
    }
}

export { retrieveFcmToken };
