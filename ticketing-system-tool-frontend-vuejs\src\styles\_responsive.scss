:root {
  @media screen and (max-width: 1279px) {
    .filter-wrapper {
      width: calc(100% - 66px);
      .v-col-sm-3 {
        flex: 0 0 33%;
        max-width: 33%;
      }
      & + .page-content-wrapper {
        top: 203px;
      }
    }
  }
  @media screen and (max-width: 991px) {
    .filter-wrapper {
      & + .page-content-wrapper {
        top: 266px;
      }
    }
    .fixed-right-sidebar {
      .page-inner-layout {
        flex-direction: column;
      }
      .page-left-column {
        width: 100%;
      }
      .page-right-column {
        width: 100%;
        position: static;
        margin-top: 20px;
        height: auto;
      }
      .custom-select {
        .select-field {
          max-width: 100% !important;
          width: 100% !important;
        }
      }
      .select-field {
        max-width: 100% !important;
        width: 100% !important;
      }
    }
    .tab-content-wrapper {
      .v-row {
        flex-direction: column;
        .v-col {
          width: 100%;
          &.d-flex {
            flex-direction: column;
          }
          & > div {
            width: 100%;
            & + div {
              margin-top: 10px;
            }
          }
        }
      }
    }
    .app-page-layout {
      .page-wrapper {
        .page-header {
          position: static;
          width: 100%;
          & > .v-row {
            & > .v-col {
              width: 100%;
              max-width: 100% !important;
              &:empty {
                display: none;
              }
              & > .d-flex {
                flex-direction: column;
              }
            }
          }
          .v-breadcrumbs {
            margin: 5px 0 0 0;
            white-space: nowrap;
            overflow: auto;
          }
          .page-title {
            margin-bottom: 6px;
            font-size: 20px;
            text-align: center;
          }
          .page-action {
            position: static;
            text-align: center;
            .btn-toolbar {
              justify-content: center;
            }
          }
        }
        .page-content-wrapper {
          top: 0;
        }
      }
    }
  }
  @media screen and (max-width: 820px) {
    .char-counter {
      color: #b00020;
      margin: 0;
      padding: 0 !important;
      justify-content: flex-start;
      .char-remaining {
        color: #b00020;
      }
      &.limit-exceeded {
        color: #b00020;
      }
    }
    .page-create-ticket {
      .form-wrapper {
        input {
          & ~ .v-btn {
            width: 100%;
          }
        }
      }
    }
  }
  @media screen and (max-width: 767px) {
    .app-page-layout{
      .page-wrapper {
        .custom-list-group{
          min-width: 100%;
        }
      }
    }
    .chat-container {
      .chat-box {
        .chat-single-container {
          & > ul {
            & > li {
              .user-comment-picture {
                position: static;
                display: flex;
                justify-content: center;
                .user-dp {
                }
              }
              .user-comment-wrapper {
                .user-comment-info {
                  h6 {
                    text-align: center;
                  }
                }
              }
              .user-comment-description {
                text-align: left !important;
                justify-content: flex-start !important;
                margin-top: 8px;
                p {
                  text-align: left !important;
                  font-size: 90%;
                }
              }
              &.sent-message,
              &.received-message {
                padding: 20px;
                margin: 0;
                width: 100%;
                flex-direction: column;
                max-width: 100%;
              }
              & + li {
                margin-top: 15px !important;
              }
            }
          }
        }
      }
    }
  }
  @media screen and (max-width: 575px) {
    //Login
    #app {
      .app-main {
        .app-layout {
          .app-login-wrapper {
            .left {
              display: none;
            }
          }
        }
      }
    }
    // Menu
    .v-navigation-drawer--active {
      border-radius: 0;
      box-shadow: none;
      height: 100% !important;
      width: 90% !important;
      top: 0 !important;
      left: 0 !important;
      z-index: 9999;
      .v-navigation-drawer__content {
        & > h1 {
          & > a {
            display: inline-block;
          }
        }
        & > .v-list {
          padding: 0;
        }
      }
    }
    .v-navigation-drawer__scrim {
      opacity: 0.5;
    }
    // Header
    .v-app-bar {
      &.app-header-wrapper {
        .v-toolbar__content {
          height: auto !important;
          & > .v-row {
            //flex-direction: row-reverse;
            & > .v-col {
              width: 100%;
              & > .v-row {
                flex-direction: row;
                justify-content: space-between !important;
                & > .v-col {
                  width: 100%;
                }
              }
            }
          }
          .notify-panel {
            position: absolute;
            top: 15px;
            right: 15px;
          }
          .user-panel {
            width: 100%;
            margin: 0 0 12px;
            padding: 10px 10px 10px 25px;
            background: #ffffff;
            position: relative;
            border-radius: 5px;
            margin-top: -18px;
            border: 1px dashed #cacaca;
            box-shadow: inset 0 0 25px 0 rgba(0, 0, 0, 0.05);
            & > .v-list {
              background: none;
            }
          }
        }
      }
    }
    // Notification
    .v-menu {
      & > .v-overlay__content {
        & > .v-list.notification-list {
          width: 100%;
          margin-top: 5px;
          .notify-panel-head {
            top: 8px;
          }
        }
      }
    }
    // Page Header
    .app-page-layout {
      top: 88px !important;
      .page-wrapper {
        height: auto;
        &.page-change-password {
          height: auto;
          .app-login-wrapper {
            &.secondary-login-wrapper {
              .right {
                min-width: auto;
              }
            }
          }
        }
        .user-view-panel {
          font-size: 14px;
          .custom-list-group {
            min-width: 100%;
            & > li {
              padding: 20px;
              .v-row {
                flex-direction: column;
                &.user-info {
                  .v-col {
                    width: 100%;
                    max-width: 100%;
                  }
                }
              }
            }
          }
        }
      }
    }
    .user-info-section {
      .form-section,
      & > div[class*="v-col"] {
        width: 100%;
        max-width: 100%;
        flex: 100%;
      }
    }
    .page-content-wrapper {
      top: 0;
    }
    .form-group {
      input,
      button,
      .v-btn {
        width: 100%;
      }
    }
    .ticket-current-info {
      .v-row {
        flex-direction: column;
        .v-col {
          width: 100%;
        }
      }
    }
    .v-timeline--vertical.v-timeline {
      row-gap: 15px;
    }
    .v-timeline--vertical.v-timeline--align-start {
      grid-template-columns: 100%;
    }
    .v-timeline--vertical.v-timeline
      .v-timeline-item:nth-child(2n + 1)
      .v-timeline-item__body,
    .v-timeline--vertical.v-timeline
      .v-timeline-item:nth-child(2n)
      .v-timeline-item__body {
      grid-column: 1;
      padding-inline-end: 0;
    }
    .card-hightlight-default {
      height: auto;
    }
    .select-field {
      max-width: 100% !important;
    }
    .card-list-wrapper {
      .app-primary-card {
        .text-right {
          text-align: center !important;
        }
        .justify-end {
          justify-content: center !important;
        }
        .actions {
          justify-content: center;
        }
        & > .v-row {
          flex-direction: column;
          .v-col {
            width: 100%;
            max-width: 100%;
            .v-row {
              flex-direction: column;
              .v-col {
                width: 100%;
                max-width: 100%;
              }
            }
          }
        }
        &.ticket-list-card {
          .ticket-calendar-stats {
            position: static;
            border-radius: 5px;
            padding: 10px;
            border-right: 1px solid #d8d8d8;
            margin-top: 10px;
            & > ul {
              flex-direction: column;
              gap: 5px;
            }
          }
        }
      }
      .btn-toolbar {
        justify-content: center;
      }
    }
    .user-avatar-sm {
      text-align: center;
    }
    .widget-charts-wrapper {
      .chart-container {
        .chart-header {
          flex-direction: column;
          .chart-title {
            margin-bottom: 10px;
          }
        }
      }
    }
    .widget-section-wrapper {
      .widget-card {
        .widget-header {
          flex-direction: column;
        }
        .widget-actions {
          flex-direction: column;
          & > * {
            width: 100%;
            margin: 10px 0 0 0;
          }
          .dropdown-wrapper {
            padding-left: 0;
          }
          .v-btn {
            margin-left: 0 !important;
          }
        }
      }
    }
    //Filter
    .filter-wrapper {
      position: static;
      width: 100%;
      padding: 15px;
      .v-col-sm-3 {
        flex: 100%;
        max-width: 100%;
      }
      & + .page-content-wrapper {
        top: 0;
      }
    }
    .ticket-switch-btn {
      position: static;
      min-width: auto;
      flex-direction: column;
      justify-content: flex-start;
      border-radius: 5px;
      margin-bottom: 20px;
      .ticket-user-filter{
        border: 0;
        padding: 0;
      }
    }
    .all-ticket-container {
      height: auto;
    }
    // Dialog
    .v-overlay-container {
      .v-dialog {
        .v-overlay__content {
          .v-card {
            .v-card-title {
            }
            .v-card-text {
              .v-row {
                flex-direction: column;
                .v-col {
                  width: 100%;
                }
                &.module-access-container {
                  flex-direction: row;
                  & > .v-col {
                    width: auto;
                    flex: 0 0 auto;
                  }
                }
              }
              .dialog-confirmation-icon {
                .v-icon {
                  font-size: 40px !important;
                }
              }
              .dialog-confirmation-title {
                font-size: 20px !important;
              }
              .dialog-confirmation-info {
                font-size: 14px !important;
                line-height: normal !important;
              }
            }
            .v-card-actions {
              flex-direction: column;
              .v-btn {
                width: 90%;
              }
            }
            .v-form {
              .v-row {
                .v-col {
                  width: 100%;
                  max-width: 100%;
                  flex: 100%;
                }
              }
            }
          }
        }
      }
    }
  }
}
