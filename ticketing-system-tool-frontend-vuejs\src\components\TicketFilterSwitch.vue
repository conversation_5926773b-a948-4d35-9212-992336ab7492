<template>
  <div class="ticket-switch-btn">
    <div class="ticket-user-filter">
      <!-- My Tickets / Team Tickets Switch -->
      <v-switch
        :model-value="selectedFilter"
        @update:model-value="updateSelectedFilter"
        :value="'my'"
        true-value="my"
        false-value="team"
        inset
        color="primary"
        :label="selectedFilter === 'my' ? 'My Tickets' : 'Team Tickets'"
        hide-details
        class="custom-switch"
      />
    </div>

    <!-- Card View / List View Switch (only show if showViewSwitch is true) -->
    <div v-if="showViewSwitch" class="ticket-view-filter">
      <v-switch
        :model-value="isKanbanView"
        @update:model-value="updateViewMode"
        inset
        color="primary"
        :label="isKanbanView ? 'Card View' : 'List View'"
        hide-details
        class="custom-switch"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, watch, onMounted } from "vue";

export default defineComponent({
  name: "TicketFilterSwitch",
  props: {
    selectedFilter: {
      type: String,
      default: "my",
      validator: (value: string) => ["my", "team"].includes(value),
    },
    isKanbanView: {
      type: Boolean,
      default: true,
    },
    showViewSwitch: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["update:selectedFilter", "update:isKanbanView"],
  setup(props, { emit }) {
    const updateSelectedFilter = (value: string | null) => {
      if (value) {
        emit("update:selectedFilter", value);
        // Save to localStorage
        localStorage.setItem("selectedFilter", value);
        console.log("Selected filter changed:", value);
      }
    };

    const updateViewMode = (value: boolean | null) => {
      if (value !== null) {
        emit("update:isKanbanView", value);
        // Save to localStorage
        localStorage.setItem("isKanbanView", String(value));
        console.log("View mode changed:", value ? "Card View" : "List View");
      }
    };

    onMounted(() => {
      // Load saved preferences from localStorage
      const storedFilter = localStorage.getItem("selectedFilter");
      if (storedFilter === "my" || storedFilter === "team") {
        if (storedFilter !== props.selectedFilter) {
          updateSelectedFilter(storedFilter);
        }
      }

      if (props.showViewSwitch) {
        const storedView = localStorage.getItem("isKanbanView");
        if (storedView !== null) {
          const isKanban = storedView === "true";
          if (isKanban !== props.isKanbanView) {
            updateViewMode(isKanban);
          }
        }
      }
    });

    return {
      updateSelectedFilter,
      updateViewMode,
    };
  },
});
</script>

<style scoped>
.ticket-switch-btn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 0;
  gap: 16px;
}

.ticket-user-filter,
.ticket-view-filter {
  display: flex;
  align-items: center;
}

.custom-switch {
  margin-right: 16px;
}

.custom-switch .v-label {
  font-weight: 500;
  color: #333;
}

/* Responsive design */
@media (max-width: 768px) {
  .ticket-switch-btn {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .custom-switch {
    margin-right: 0;
  }
}
</style>
