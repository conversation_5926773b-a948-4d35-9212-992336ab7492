export const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "http://127.0.0.1:8000/api/";

export const API_ENDPOINTS = {
  GET_ALL_TICKETS: "tickets/",
  GET_LOCATIONS: "users/location/",
  GET_TICKET_STATUS: "ticket-status/",
  GET_TICKET_STATUSTRACKING: "status-tracking/",
  GET_TICKET_APPROVAL_STATUS: "ticket-approvalstatus/",
  GET_TICKET_BY_ID: "tickets/",
  GET_TICKET_BY_PAGE: "tickets-all/",
  GET_TICKET_BY_CHAT: "chats/?ticket_id=",
  // GET_TICKET_BY_CHAT_ID: "ticket/{ticket_id}/messages/",
  GET_TICKET_BY_CHAT_ID: "upload_chat_attachment/",
  GET_NOTIFICATION: "notifications/",
  GET_NOTIFICATION_BYID: "notifications/",
  GET_CLEAR_NOTIFICATION_BY_ID: "notifications/clear/",
  UPDATE_NOTIFICATION: "notifications/update/",
  GET_PRIORITY: "tickets-priority/",
  GET_USER_ROLES: "users/role/",
  CREATE_USER_ROLE: "users/role/create",
  POST_NEW_USER: "users/create",
  GET_ALL_USERS: "users/",
  GET_USERS_PAGE: "users/page/",
  GET_USER_BY_ID: "users/",
  UPDATE_USER_BY_ID: "users/update/",
  BLOCK_USER: "users/block_unblock/",
  REMOVE_USER: "users/delete/",
  SEARCH_AND_FILTER_USER: "users/search_and_filter_users",
  CREATE_PROJECT: "users/project/create",
  GET_ALL_PROJECT: "users/project/",
  GET_PROJECT_ID: "users/project/",
  GET_PROJECT_USER: "users/project/userId/",
  UPDATE_PROJECT_BY_ID: "users/project/update_mapping",
  DELETE_PROJECT_BY_ID: "users/project/delete_project_and_tickets",
  LOCATION_REPORT: "report/location",
  CATEGORY_REPORT: "report/category_report",
  TECHNICIAN_CATEGORY_REPORT: "report/technician/category_report",
  TECHNICIAN_FEEDBACK: "report/feedback/technician",
  CATEGORIES_FEEDBACK: "report/feedback/category/",
  TICKETS_COUNT: "report/count",
  YEARLY_REPORT: "report/year_report",
  IMPORT_FILE: "users/import-file/",
  GET_USER_MODULE_ACCESS: "user_access/",
  UPDATE_USER_MODULE_ACCESS: "user_access/update/",
  EMPLOYEE_TICKET_REPORT: "report/employee_report",
  EXPORT_EMPLOYEE_TICKET_REPORT: "report/export_employee_report",
  POST_CATEGORIES: "categories/",
  GET_CATEGORIES: "categories/",
  GET_ALLCATEGORIES: "categories/without-pagination/",
  GET_CATEGORIES_BY_ID: "categories/id/",
  PUT_CATEGORIES_BY_ID: "categories/",
  DELETE_CATEGORIES_BY_ID: "categories/",
  ACTIVE_CATEGORIES_BY_ID: "categories/",
  POST_SUBCATEGORIES: "subcategories/",
  GET_SUBCATEGORIES: "subcategories/",
  GET_SUBCATEGORIES_BY_ID: "subcategories/id/",
  PUT_SUBCATEGORIES_BY_ID: "subcategories/",
  DELETE_SUBCATEGORIES_BY_ID: "subcategories/",
  RESET_PASSWORD: "password-reset-confirm",
  CHANGE_PASSWORD: "change-password/",
  FORGOTPASSWORD: "forgot-password-request",
  LOGIN: "login",
  VALIDATE_TOKEN: "validate-reset-token/",
  FETCHSTATUSES: "ticket-status/",
  USER: "users/",
  TICKETS: "tickets/",
  TICKET_STATUS: "ticket-status",
  TICKET_CATEGORIES: "categories/",
  TICKET_SUBCATEGORIES: "subcategories/",
  FEEDBACK_VALUES: "feedback-values/",
  CREATE_FEEDBACK: "feedback-values/create",
  FCM_TOKEN: "save-fcm-token/",
};
