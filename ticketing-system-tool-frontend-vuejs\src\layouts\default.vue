<template>
  <v-app id="app-layout-default" class="app-layout-root">
    <v-navigation-drawer v-model="drawer" app class="nav-bar app-sidebar">
      <h1>
        <a href="/" title="Ticketing System Portal">
          <img src="../assets/icons/logo.svg" alt="Ticketing System Portal" />
        </a>
      </h1>
      <v-list dense>
        <template v-for="module in filteredModules" :key="module.module_id">
          <!-- Regular Navigation Items -->
          <v-list-item
            v-if="!module.sub_module || module.sub_module.length === 0"
            class="text-size"
            :class="{ 'active-item': activeItem === module.module_name }"
            @click="
              () => {
                setActive(module.module_name);
                router.push(getRoute(module.module_name));
              }
            "
          >
            <template #prepend>
              <img
                :src="getIcon(module.module_name)"
                :alt="`${module.module_name} Icon`"
                class="custom-icon"
                :class="{ 'active-icon': activeItem === module.module_name }"
              />
            </template>

            <v-list-item-title>
              {{ module.module_name }}
            </v-list-item-title>
          </v-list-item>

          <!-- Dropdown for Modules with Submodules -->
          <v-list-group
            v-else
            v-model="openGroups[module.module_id]"
            :class="{ 'active-group': activeItem === module.module_name }"
          >
            <template #activator="{ props }">
              <v-list-item v-bind="props" class="text-size">
                <template #prepend>
                  <img
                    :src="getIcon(module.module_name)"
                    :alt="`${module.module_name} Icon`"
                    class="custom-icon"
                    :class="{
                      'active-icon': activeItem === module.module_name,
                    }"
                  />
                </template>
                <v-list-item-title>
                  {{ module.module_name }}
                </v-list-item-title>
              </v-list-item>
            </template>

            <v-list
              v-for="sub in getFilteredSubModules(module)"
              :key="sub.submodule_id"
              :class="{ 'active-item': activeItem === sub.submodule_name }"
            >
              <v-list-item
                @click="
                  () => {
                    setActive(sub.submodule_name);
                    router.push(getRoute(sub.submodule_name));
                  }
                "
              >
                <v-list-item-title class="submodule-container">
                  {{ sub.submodule_name }}
                </v-list-item-title>
              </v-list-item>
            </v-list>
          </v-list-group>
        </template>
      </v-list>
    </v-navigation-drawer>

    <v-app-bar class="app-header-wrapper">
      <v-row align="center" justify="space-between">
        <v-col cols="auto">
          <v-row align="center" justify="center">
            <v-col cols="auto">
              <v-icon
                class="app-sidebar-action-collapse"
                @click="drawer = !drawer"
              >
                mdi-backburger
              </v-icon>
              <v-icon class="app-sidebar-action-open" @click="drawer = !drawer">
                mdi-menu-close
              </v-icon>
            </v-col>
            <!-- <v-col cols="auto">
              <div class="global-search">
                <v-text-field
                  append-inner-icon="mdi-magnify"
                  :type="visible ? 'text' : 'text'"
                  density="compact"
                  placeholder="Search Here..."
                  variant="outlined"
                  @click:append-inner="toggleVisibility"
                />
              </div>
            </v-col> -->
          </v-row>
        </v-col>
        <v-col cols="auto">
          <v-row align="center" justify="center">
            <v-col cols="auto">
              <div class="notify-panel" @click="toggleMenu">
                <v-menu v-model="menu" offset-y transition="slide-y-transition">
                  <template #activator="{ props }">
                    <v-btn class="text-none" stacked v-bind="props">
                      <v-badge color="primary" :content="notifications.length">
                        <v-icon>mdi-bell-outline</v-icon>
                      </v-badge>
                    </v-btn>
                  </template>

                  <v-list class="notification-list">
                    <div class="notify-panel-head">
                      <v-list-title class="notification-title">
                        {{strings.logout.notification}}
                      </v-list-title>
                      <v-btn
                        v-if="notifications.length > 0"
                        color="error"
                        @click="clearNotifications"
                      >
                        {{strings.logout.clearAll}}
                      </v-btn>
                    </div>

                    <v-list-item v-if="notifications.length === 0">
                      <v-list-item-title class="no-notifications">
                        <v-icon class="mr-1"> mdi-alert-circle </v-icon>
                        <span class="d-block">{{strings.logout.noNotification}}</span>
                      </v-list-item-title>
                    </v-list-item>

                    <v-list-item
                      v-for="(notification, index) in notifications"
                      :key="notification.id || index"
                      class="notification-item"
                      :class="{ unread: !notification.is_read }"
                      @click="handleNotificationClick(notification)"
                    >
                      <v-list-item-title>
                        {{ notification.message }}
                      </v-list-item-title>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>
            </v-col>
            <v-col cols="auto">
              <!-- User info and menu -->
              <div class="user-panel">
                <v-list>
                  <v-list-item>
                    <template #prepend>
                      <v-avatar size="40">
                        <v-img
                          v-if="profile?.profile_pic"
                          :src="profile.profile_pic"
                          alt="User Image"
                        />
                        <span v-else class="user-initials">{{
                          getUserInitials(user)
                        }}</span>
                      </v-avatar>
                      <div class="user-login-info">
                        <label>{{strings.logout.welcome}}</label>
                        <strong
                          >{{ user.first_name }} {{ user.last_name }}</strong
                        >
                      </div>
                      <!-- Dropdown Menu Button -->
                      <v-menu v-model="menuVisible" offset-y>
                        <template #activator="{ props }">
                          <v-btn
                            icon="mdi-dots-vertical"
                            size="small"
                            variant="text"
                            v-bind="props"
                          />
                        </template>

                        <v-list>
                          <v-list-item
                            :ripple="false"
                            @click="myAccount(userId)"
                          >
                            <v-list-item-title>
                              <v-icon>mdi-account</v-icon>&nbsp;{{strings.logout.profileIcon.myAccount}}
                            </v-list-item-title>
                          </v-list-item>
                          <v-list-item :ripple="false" @click="resetPassword">
                            <v-list-item-title>
                              <v-icon>mdi-lock-reset</v-icon>&nbsp;{{strings.logout.profileIcon.changePassword}}
                            </v-list-item-title>
                          </v-list-item>
                          <!-- <v-list-item :ripple="false">
                            <v-list-item-title>
                              <v-icon>mdi-cog</v-icon>&nbsp;Settings
                            </v-list-item-title>
                          </v-list-item> -->
                          <v-list-item @click="logout">
                            <v-list-item-title>
                              <v-icon>mdi-logout</v-icon>&nbsp;{{strings.logout.profileIcon.logout}}
                            </v-list-item-title>
                          </v-list-item>
                        </v-list>
                      </v-menu>
                    </template>
                  </v-list-item>
                </v-list>
              </div>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-app-bar>

    <v-main class="app-page-layout">
      <slot />
    </v-main>
  </v-app>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, watch, onUnmounted } from "vue";
import { getUserModuleAccess, updateUserNotifications, getUserById } from "../api/apiClient";
import { useRouter } from "vue-router";
import dashboardIcon from "../../src/assets/icons/dashboard.svg";
import ticketManagementIcon from "../../src/assets/icons/management.svg";
import userManagementIcon from "../../src/assets/icons/Group_fill.svg";
import reportAndAnalyticsIcon from "../../src/assets/icons/analays.svg";
import itSupportConfigIcon from "../../src/assets/icons/it_support.svg";
import {
  clearUserNotifications,
  getNotification,
  getNotificationById,
  updateNotificationStatus,
} from "../api/apiClient";
import { useUserStore } from "../stores/userStore";
import strings from "../assets/strings.json";

interface Module {
  module_id: number;
  module_name: string;
  has_access: boolean;
  sub_module?: SubModule[]; // Optional if submodules exist
}

interface SubModule {
  submodule_id: number;
  submodule_name: string;
  has_access: boolean;
}
interface Notification {
  id: number;
  ticket: number;
  message: string;
  is_read: boolean;
  is_clear: boolean;
}

const router = useRouter();
const drawer = ref(true);
const activeItem = ref("");
const menuVisible = ref(false);
const modules = ref<Module[]>([]);
const openGroups = ref<Record<number, boolean>>({});
const user = JSON.parse(localStorage.getItem("user") || "{}");
const visible = ref(false);
const menu = ref<boolean>(false);
const notifications = ref<any>([]);
let notificationInterval: any = null;
const userStore = useUserStore();
const profile = computed(() => userStore.user);
const profileLoaded = ref(false);
console.log(modules);


// Open notification menu and fetch notifications
const toggleMenu = () => {
  menu.value = !menu.value;
  if (menu.value) {
    getUserNotification();
  }
};
console.log(menu);
const userId = user.id;

const routes: Record<string, string> = {
  Dashboard: "/",
  "All Ticket": "/all-tickets",
  "Create Ticket": "/create-tickets",
  "Pending Approval": "/pending-approval",
  "Unassign Ticket": "/assign-ticket",
  // "Ticket Lists": "/ticket-list",
  "Manage Users": "/manage-users",
  "Add New User": "/add-new-user",
  "Role Assignment": "/role-assignement",
  "Project Mapping": "/project-mapping",
  "Tickets & Reports": "/ticketsAndReports",
  "Technician Performance": "/technicianPerformance",
  "User Feedback": "/userFeedback",
  "Ticket Categories": "/ticketcategories",
  "Email Template": "/email-template",
};
// const handleClick = (moduleName: string) => {
//   setActive(moduleName);

//   // Wait for navigation to complete before reloading
//   setTimeout(() => {
//     window.location.reload();
//   }, 100); // short delay to let navigation happen first
// };
const toggleVisibility = () => {
  visible.value = !visible.value;
};

const setActive = (moduleName: string) => {
  activeItem.value = moduleName;
  localStorage.setItem("activeModule", moduleName);
};

const fetchModuleAccess = async () => {
  try {
    const response = await getUserModuleAccess(user.role);
    modules.value = response.data[0]?.modules || [];
    console.log(modules.value);
    
  } catch (error: any) {
    console.error("Failed to fetch module access: ", error);
  }
};

const getIcon = (moduleName: string) => {
  const icons: Record<string, string> = {
    Dashboard: dashboardIcon,
    "Ticket Management": ticketManagementIcon,
    "User Management": userManagementIcon,
    "Report & Analytics": reportAndAnalyticsIcon,
    "IT Support Config": itSupportConfigIcon,
  };
  return icons[moduleName] || "../assets/icons/default.svg";
};

const getRoute = (moduleName: string) => {
  return routes[moduleName] || "/";
};

const filteredModules = computed(() => {
  return modules.value.filter((module: Module) => module.has_access);
});

const getFilteredSubModules = (module: Module) => {
  return module.sub_module?.filter((sub: SubModule) => sub.has_access) || [];
};

const getUserInitials = (user: any) => {
  if (!user.first_name || !user.last_name) return "U"; // Default Initial
  return (
    user.first_name.charAt(0).toUpperCase() +
    user.last_name.charAt(0).toUpperCase()
  );
};

const handleNotificationClick = async (notification: Notification) => {
  // Log clicked notification and the current notifications array for debugging
  console.log("Clicked notification:", notification);
  console.log("All notifications:", notifications.value);

  // Option 1: Filter only unread notifications for this ticket
  let notificationsForTicket = notifications.value.filter(
    (n: Notification) => n.ticket === notification.ticket && !n.is_read
  );

  // If no unread notifications are found, log a warning
  if (notificationsForTicket.length === 0) {
    console.warn(
      "No unread notifications found for this ticket. Updating all notifications for the ticket."
    );
    // Option 2: Alternatively, update all notifications for this ticket:
    notificationsForTicket = notifications.value.filter(
      (n: Notification) => n.ticket === notification.ticket
    );
  }

  // Create an array of notification IDs to update
  const notificationIds = notificationsForTicket.map((n: Notification) => n.id);
  console.log("Notification IDs to update:", notificationIds);

  // Call your API to update these notifications (mark as read and clear)
  try {
    await updateUserNotifications({ notifications: notificationIds });
    // Optionally update the local state for these notifications:
    notifications.value.forEach((n: Notification) => {
      if (n.ticket === notification.ticket) {
        n.is_read = true;
        n.is_clear = true;
      }
    });
  } catch (error: any) {
    console.error("Error updating notifications:", error);
    return;
  }

  // Then, navigate to the ticket's edit page
  if (notification.ticket) {
    await router.push(`/all-tickets/view-tickets/${notification.ticket}/edit`);
    window.location.reload();
  } else {
    console.warn("Notification does not have a ticket field");
  }

  // Finally, remove notifications for this ticket from the UI
  notifications.value = notifications.value.filter(
    (n: Notification) => n.ticket !== notification.ticket
  );
};

// const getUserNotification = async () => {
//   const response = await getNotificationById({ id: user.id });
//   console.log("Notifications", response.data);
//   notifications.value = response.data;
// };
const getUserNotification = async () => {
  try {
    const response = await getNotificationById({ id: user.id });
    notifications.value = response.data.filter(
      (notification: any) => !notification.is_clear
    );
    await markNotificationsAsRead(user.id);
  } catch (error: any) {
    console.error("Error fetching notifications:", error);
  }
};


const markNotificationsAsRead = async (userId: number) => {
  try {
    await updateNotificationStatus({ id: userId });
    notifications.value.forEach(
      (notification: any) => (notification.is_read = true)
    ); // Update UI
  } catch (error: any) {
    console.error("Error marking notifications as read:", error);
  }
};

// Clear all notifications
const clearNotifications = async () => {
  try {
    await clearUserNotifications({ id: user.id });
    notifications.value = []; // Clear UI notifications
  } catch (error: any) {
    console.error("Error clearing notifications:", error);
  }
};
// Open notification menu and fetch notifications

const resetPassword = async () => {
  await router.push({ path: "/changepassword", query: { internal: "true" } });
};

const myAccount = async (userId: number) => {
  await router.push({
    name: "/User Management/viewUser",
    params: { id: userId },
    query: { self: "true" },
  });
};

const logout = async () => {
  localStorage.clear();
  await router.push("/login");
};

const fetchUserDetails = async () => {
  try {
    const userId = user.id;
    const userData = await getUserById({ id: userId });
    userStore.setUser(userData.data); // only store .data part!
    profileLoaded.value = true;
    console.log("Fetched user profile:", userData.data);
  } catch (error) {
    console.error("Error fetching user profile:", error);
  }
};

watch(
  () => router.currentRoute.value.path,
  (newPath) => {
    const matchingModule = Object.entries(routes).find(
      ([_, path]) => path === newPath
    );
    if (matchingModule) {
      setActive(matchingModule[0]);
    }
  },
  { immediate: true }
);

onMounted(async () => {
  await fetchModuleAccess();
  await getUserNotification();
  await fetchUserDetails();
  const savedActiveModule = localStorage.getItem("activeModule") || "Dashboard";
  setActive(savedActiveModule);
  notificationInterval = setInterval(getUserNotification, 30000);
});

onUnmounted(() => {
  if (notificationInterval) {
    clearInterval(notificationInterval);
  }
});
</script>

<style lang="scss" scoped>
.notify-panel {
  position: relative;
}
</style>
