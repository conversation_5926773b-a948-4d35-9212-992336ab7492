{
  "files": [],
  "compilerOptions": {
    "resolveJsonModule": true,
    "moduleResolution": "node",
    "esModuleInterop": true,
    "allowJs": true,
    "module": "esnext", 
    "target": "esnext", 
    "types": ["vuetify", "pinia"],
    "lib": ["ES2019", "DOM"],
    "skipLibCheck": true,
    "isolatedModules": false,

  },
  "references": [
    {
      "path": "./tsconfig.node.json"
    },
    {
      "path": "./tsconfig.app.json"
    }
  ]
}
